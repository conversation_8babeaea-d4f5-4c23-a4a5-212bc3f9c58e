<template>
  <div class="dashboard-view min-h-screen bg-gradient-to-br from-base-100 via-base-200/30 to-base-300/20">
    <!-- Enhanced Dashboard Header -->
    <div class="glass-header bg-base-100/80 backdrop-blur-md border-b border-base-300/50 sticky top-0 z-40">
      <div class="container mx-auto px-4 lg:px-6">
        <div class="flex items-center justify-between h-16 lg:h-20">
          <!-- Left Section -->
          <div class="flex items-center space-x-6 lg:space-x-8">
            <!-- Back to Home Button -->
            <RouterLink to="/" class="btn btn-ghost btn-sm hidden lg:flex items-center space-x-2 hover:bg-base-200/50">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
              </svg>
              <span>Back to Site</span>
            </RouterLink>

            <div class="flex items-center space-x-3">
              <div class="w-8 h-8 lg:w-10 lg:h-10 rounded-xl bg-gradient-to-br from-primary to-primary-focus flex items-center justify-center shadow-lg">
                <svg class="w-5 h-5 lg:w-6 lg:h-6 text-primary-content" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
              </div>
              <div>
                <h1 class="text-xl lg:text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                  HLenergy Dashboard
                </h1>
                <p class="text-xs text-base-content/60 hidden lg:block">Energy Management Platform</p>
              </div>
            </div>

            <!-- Enhanced Navigation -->
            <nav class="hidden lg:flex space-x-1">
              <button
                v-for="tab in navigationTabs"
                :key="tab.id"
                @click="activeTab = tab.id; analytics.trackTabChange(tab.id)"
                :class="[
                  'relative px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300',
                  activeTab === tab.id
                    ? 'text-primary bg-primary/10 shadow-md'
                    : 'text-base-content/70 hover:text-primary hover:bg-base-200/50'
                ]"
              >
                <span class="flex items-center space-x-2">
                  <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                    <path :d="tab.iconPath" />
                  </svg>
                  <span>{{ tab.label }}</span>
                </span>
                <div
                  v-if="activeTab === tab.id"
                  class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1/2 h-0.5 bg-gradient-to-r from-primary to-secondary rounded-full"
                ></div>
              </button>
            </nav>
          </div>

          <!-- Right Section -->
          <div class="flex items-center space-x-3 lg:space-x-4">
            <!-- Quick Stats Badge -->
            <div class="hidden md:flex items-center space-x-2 px-3 py-1.5 bg-success/10 text-success rounded-full text-xs font-medium">
              <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
              <span>{{ totalLeads }} leads this month</span>
            </div>

            <!-- Theme Toggle -->
            <button
              @click="toggleTheme"
              class="btn btn-ghost btn-sm btn-circle hover:bg-base-200/50"
              title="Toggle theme"
            >
              <svg v-if="isDarkTheme" class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
              </svg>
              <svg v-else class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
              </svg>
            </button>

            <!-- User Profile with Dropdown -->
            <div class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="flex items-center space-x-3 cursor-pointer hover:bg-base-200/50 rounded-lg p-2 transition-colors">
                <div class="hidden md:block text-right">
                  <div class="text-sm font-medium text-base-content">{{ authStore.user?.name }}</div>
                  <div class="text-xs text-base-content/60">{{ authStore.user?.role || 'User' }}</div>
                </div>
                <div class="avatar">
                  <div class="w-10 h-10 rounded-full bg-gradient-to-br from-primary to-secondary text-primary-content flex items-center justify-center text-sm font-bold shadow-lg ring-2 ring-primary/20">
                    {{ authStore.userInitials }}
                  </div>
                </div>
                <svg class="w-4 h-4 text-base-content/60" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M7 10l5 5 5-5z"/>
                </svg>
              </div>
              <ul tabindex="0" class="dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow-xl border border-base-300/50">
                <li>
                  <RouterLink to="/profile" class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                    </svg>
                    <span>Profile</span>
                  </RouterLink>
                </li>
                <li>
                  <RouterLink to="/" class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                    <span>Home</span>
                  </RouterLink>
                </li>
                <div class="divider my-1"></div>
                <li>
                  <button @click="logout" class="flex items-center space-x-2 text-error hover:bg-error/10">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                    </svg>
                    <span>Logout</span>
                  </button>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="lg:hidden mt-4 px-4">
          <div class="flex space-x-1 overflow-x-auto pb-2">
            <button
              v-for="tab in navigationTabs"
              :key="tab.id"
              @click="activeTab = tab.id; analytics.trackTabChange(tab.id)"
              :class="[
                'flex-shrink-0 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 whitespace-nowrap',
                activeTab === tab.id
                  ? 'text-primary bg-primary/10'
                  : 'text-base-content/70 hover:text-primary hover:bg-base-200/50'
              ]"
            >
              <span class="flex items-center space-x-2">
                <component :is="tab.icon" class="w-4 h-4" />
                <span>{{ tab.label }}</span>
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Enhanced Dashboard Content -->
    <div class="container mx-auto px-4 lg:px-6 py-6 lg:py-8">
      <!-- Overview Tab -->
      <div v-if="activeTab === 'overview'" class="space-y-8">
        <!-- Welcome Section -->
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h2 class="text-2xl lg:text-3xl font-bold text-base-content">
              Welcome back, {{ authStore.user?.name?.split(' ')[0] || 'User' }}! 👋
            </h2>
            <p class="text-base-content/70 mt-1">Here's what's happening with your energy management today.</p>
          </div>
          <div class="flex items-center space-x-3">
            <div class="text-sm text-base-content/60">
              {{ new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' }) }}
            </div>
          </div>
        </div>

        <!-- Enhanced Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-6">
          <!-- Total Leads Card -->
          <div class="stats-card group">
            <div class="card glass-effect bg-gradient-to-br from-primary/5 to-primary/10 border border-primary/20 hover:border-primary/40 transition-all duration-300 hover:shadow-glow">
              <div class="card-body p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <div class="w-10 h-10 rounded-lg bg-primary/20 flex items-center justify-center">
                        <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M16 4v4h4V4h-4zm-2-2h8v8h-8V2zM4 4v4h4V4H4zM2 2h8v8H2V2zm2 12v4h4v-4H4zm-2-2h8v8H2v-8zm12 0v2h2v-2h-2zm0 4v2h2v-2h-2zm2-6v2h2v-2h-2zm0 4v2h2v-2h-2zm2-4v2h2v-2h-2z"/>
                        </svg>
                      </div>
                      <h3 class="text-sm font-medium text-base-content/70">Total Leads</h3>
                    </div>
                    <div class="text-3xl font-bold text-primary mb-1">{{ totalLeads }}</div>
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-success">+12%</span>
                      <span class="text-xs text-base-content/60">vs last month</span>
                    </div>
                  </div>
                  <div class="text-primary/30 group-hover:text-primary/50 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Conversion Rate Card -->
          <div class="stats-card group">
            <div class="card glass-effect bg-gradient-to-br from-secondary/5 to-secondary/10 border border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-glow-sage">
              <div class="card-body p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <div class="w-10 h-10 rounded-lg bg-secondary/20 flex items-center justify-center">
                        <svg class="w-5 h-5 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
                        </svg>
                      </div>
                      <h3 class="text-sm font-medium text-base-content/70">Conversion Rate</h3>
                    </div>
                    <div class="text-3xl font-bold text-secondary mb-1">{{ conversionRate.toFixed(1) }}%</div>
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-success">+2.4%</span>
                      <span class="text-xs text-base-content/60">vs last month</span>
                    </div>
                  </div>
                  <div class="text-secondary/30 group-hover:text-secondary/50 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M7 14l5-5 5 5z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Active Users Card -->
          <div class="stats-card group">
            <div class="card glass-effect bg-gradient-to-br from-accent/5 to-accent/10 border border-accent/20 hover:border-accent/40 transition-all duration-300 hover:shadow-glow-gold">
              <div class="card-body p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <div class="w-10 h-10 rounded-lg bg-accent/20 flex items-center justify-center">
                        <svg class="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                      </div>
                      <h3 class="text-sm font-medium text-base-content/70">Active Users</h3>
                    </div>
                    <div class="text-3xl font-bold text-accent mb-1">{{ totalUsers }}</div>
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-success">+8.2%</span>
                      <span class="text-xs text-base-content/60">last 7 days</span>
                    </div>
                  </div>
                  <div class="text-accent/30 group-hover:text-accent/50 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M17 21v-2a4 4 0 00-4-4H5a4 4 0 00-4 4v2M23 21v-2a4 4 0 00-3-3.87M16 3.13a4 4 0 010 7.75M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Qualified Leads Card -->
          <div class="stats-card group">
            <div class="card glass-effect bg-gradient-to-br from-success/5 to-success/10 border border-success/20 hover:border-success/40 transition-all duration-300 hover:shadow-lg">
              <div class="card-body p-6">
                <div class="flex items-start justify-between">
                  <div class="flex-1">
                    <div class="flex items-center space-x-2 mb-2">
                      <div class="w-10 h-10 rounded-lg bg-success/20 flex items-center justify-center">
                        <svg class="w-5 h-5 text-success" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                      </div>
                      <h3 class="text-sm font-medium text-base-content/70">Qualified Leads</h3>
                    </div>
                    <div class="text-3xl font-bold text-success mb-1">{{ qualifiedLeads }}</div>
                    <div class="flex items-center space-x-2">
                      <span class="text-sm text-success">Ready</span>
                      <span class="text-xs text-base-content/60">for contact</span>
                    </div>
                  </div>
                  <div class="text-success/30 group-hover:text-success/50 transition-colors">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M5 13l4 4L19 7"/>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Enhanced Activity Section -->
        <div class="grid grid-cols-1 xl:grid-cols-3 gap-6">
          <!-- Recent Leads -->
          <div class="xl:col-span-2">
            <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
              <div class="card-body p-6">
                <div class="flex items-center justify-between mb-6">
                  <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
                      <svg class="w-5 h-5 text-primary" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                      </svg>
                    </div>
                    <h3 class="text-lg font-semibold text-base-content">Recent Leads</h3>
                  </div>
                  <button @click="activeTab = 'leads'" class="btn btn-primary btn-sm">
                    View All
                    <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 5l7 7-7 7"/>
                    </svg>
                  </button>
                </div>

                <div class="space-y-4">
                  <div v-for="(lead, i) in recentLeads" :key="i"
                       class="group p-4 bg-gradient-to-r from-base-200/50 to-base-200/30 rounded-xl border border-base-300/30 hover:border-primary/30 transition-all duration-300 hover:shadow-md">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-4">
                        <div class="w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                          <span class="text-sm font-bold text-primary">{{ lead.initials }}</span>
                        </div>
                        <div>
                          <div class="font-semibold text-base-content group-hover:text-primary transition-colors">
                            {{ lead.name }}
                          </div>
                          <div class="text-sm text-base-content/70">{{ lead.service }}</div>
                          <div class="text-xs text-base-content/50">{{ lead.email }}</div>
                        </div>
                      </div>
                      <div class="text-right space-y-1">
                        <div class="text-sm text-base-content/70">{{ lead.timeAgo }}</div>
                        <div class="badge badge-primary badge-sm">{{ lead.status }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Top Services -->
          <div class="xl:col-span-1">
            <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
              <div class="card-body p-6">
                <div class="flex items-center space-x-3 mb-6">
                  <div class="w-10 h-10 rounded-lg bg-secondary/10 flex items-center justify-center">
                    <svg class="w-5 h-5 text-secondary" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <h3 class="text-lg font-semibold text-base-content">Top Services</h3>
                </div>

                <div class="space-y-4">
                  <div v-for="(service, index) in topServices" :key="service.name"
                       class="group p-3 rounded-lg hover:bg-base-200/50 transition-all duration-300">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 rounded-lg flex items-center justify-center text-xs font-bold"
                             :class="getServiceBadgeClass(index)">
                          {{ index + 1 }}
                        </div>
                        <span class="font-medium text-base-content">{{ service.name }}</span>
                      </div>
                      <span class="text-sm font-bold text-primary">{{ service.count }}</span>
                    </div>
                    <div class="flex items-center space-x-2">
                      <div class="flex-1 bg-base-300 rounded-full h-2">
                        <div
                          class="h-2 rounded-full transition-all duration-500 ease-out"
                          :class="getServiceProgressClass(index)"
                          :style="{ width: `${service.percentage}%` }"
                        ></div>
                      </div>
                      <span class="text-xs text-base-content/60">{{ service.percentage }}%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <!-- Quick Actions -->
          <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
            <div class="card-body p-6">
              <div class="flex items-center space-x-3 mb-6">
                <div class="w-10 h-10 rounded-lg bg-accent/10 flex items-center justify-center">
                  <svg class="w-5 h-5 text-accent" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-base-content">Quick Actions</h3>
              </div>

              <div class="grid grid-cols-2 gap-3">
                <button class="btn btn-primary btn-sm justify-start" @click="activeTab = 'leads'">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                  </svg>
                  New Lead
                </button>
                <button class="btn btn-secondary btn-sm justify-start" @click="activeTab = 'analytics'">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z"/>
                  </svg>
                  View Analytics
                </button>
                <button class="btn btn-accent btn-sm justify-start" @click="exportReport">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z"/>
                  </svg>
                  Export Report
                </button>
                <button class="btn btn-info btn-sm justify-start" @click="activeTab = 'crm'">
                  <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z"/>
                  </svg>
                  CRM Dashboard
                </button>
              </div>
            </div>
          </div>

          <!-- System Status -->
          <div class="card glass-effect bg-base-100/80 backdrop-blur-sm border border-base-300/50 shadow-xl">
            <div class="card-body p-6">
              <div class="flex items-center space-x-3 mb-6">
                <div class="w-10 h-10 rounded-lg bg-success/10 flex items-center justify-center">
                  <svg class="w-5 h-5 text-success" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                  </svg>
                </div>
                <h3 class="text-lg font-semibold text-base-content">System Status</h3>
              </div>

              <div class="space-y-4">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content/70">API Status</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-success">Online</span>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content/70">Database</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-success">Connected</span>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content/70">Email Service</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-success rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-success">Active</span>
                  </div>
                </div>
                <div class="flex items-center justify-between">
                  <span class="text-sm text-base-content/70">Cache</span>
                  <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-warning rounded-full animate-pulse"></div>
                    <span class="text-sm font-medium text-warning">Optimizing</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Analytics Tab -->
      <div v-if="activeTab === 'analytics'">
        <AnalyticsDashboard />
      </div>

      <!-- Leads Tab -->
      <div v-if="activeTab === 'leads'" class="space-y-6">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-bold">Lead Management</h2>
          <button class="btn btn-primary">Export Leads</button>
        </div>
        
        <div class="card bg-base-100 shadow-lg">
          <div class="card-body">
            <div class="overflow-x-auto">
              <table class="table table-zebra w-full">
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Email</th>
                    <th>Service Interest</th>
                    <th>Status</th>
                    <th>Date</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-if="recentLeads.length === 0">
                    <td colspan="6" class="text-center text-base-content/60 py-8">
                      No leads found. Leads will appear here when customers submit the contact form.
                    </td>
                  </tr>
                  <tr v-else v-for="lead in recentLeads.slice(0, 10)" :key="lead.id">
                    <td class="font-medium">{{ lead.name }}</td>
                    <td>{{ lead.email }}</td>
                    <td>{{ lead.service }}</td>
                    <td>
                      <div class="badge" :class="{
                        'badge-primary': lead.status === 'New',
                        'badge-info': lead.status === 'Contacted',
                        'badge-success': lead.status === 'Qualified',
                        'badge-warning': lead.status === 'Follow-up'
                      }">{{ lead.status }}</div>
                    </td>
                    <td>{{ lead.timeAgo }}</td>
                    <td>
                      <RouterLink :to="`/crm/customers/${lead.id}`" class="btn btn-sm btn-ghost">View</RouterLink>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <!-- Reports Tab -->
      <div v-if="activeTab === 'reports'" class="space-y-6">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-bold">Business Reports</h2>
          <button @click="exportReport" class="btn btn-primary">Generate Report</button>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Monthly Performance</h3>
              <div class="text-center py-8">
                <div class="text-4xl font-bold text-primary">{{ conversionRate.toFixed(1) }}%</div>
                <div class="text-lg">Average Conversion Rate</div>
                <div class="text-sm text-base-content/70 mt-2">
                  Based on last 30 days of data
                </div>
              </div>
            </div>
          </div>

          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Revenue Forecast</h3>
              <div class="text-center py-8">
                <div class="text-4xl font-bold text-success">${{ (totalLeads * 2500).toLocaleString() }}</div>
                <div class="text-lg">Potential Revenue</div>
                <div class="text-sm text-base-content/70 mt-2">
                  Estimated based on current leads
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- CRM Tab -->
      <div v-if="activeTab === 'crm'" class="space-y-6">
        <div class="flex items-center justify-between">
          <h2 class="text-2xl font-bold">Customer Relationship Management</h2>
          <RouterLink to="/crm/dashboard" class="btn btn-primary">Open CRM</RouterLink>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          <!-- CRM Quick Access -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Quick Access</h3>
              <div class="space-y-3">
                <RouterLink to="/crm/customers" class="btn btn-block btn-sm btn-primary">
                  👥 Manage Customers
                </RouterLink>
                <RouterLink to="/crm/projects" class="btn btn-block btn-sm btn-secondary">
                  📋 View Projects
                </RouterLink>
                <RouterLink to="/crm/communications" class="btn btn-block btn-sm btn-accent">
                  💬 Communication Hub
                </RouterLink>
                <RouterLink to="/crm/customers/new" class="btn btn-block btn-sm btn-info">
                  + Add New Customer
                </RouterLink>
              </div>
            </div>
          </div>

          <!-- CRM Stats -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">CRM Overview</h3>
              <div class="space-y-3">
                <div class="flex justify-between">
                  <span>Total Customers</span>
                  <span class="font-bold">{{ totalCustomers || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>Active Projects</span>
                  <span class="font-bold">{{ activeProjects?.length || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>Unread Messages</span>
                  <span class="font-bold text-warning">{{ unreadCommunications?.length || 0 }}</span>
                </div>
                <div class="flex justify-between">
                  <span>Conversion Rate</span>
                  <span class="font-bold text-success">{{ conversionRate.toFixed(1) }}%</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent CRM Activity -->
          <div class="card bg-base-100 shadow-lg">
            <div class="card-body">
              <h3 class="card-title">Recent Activity</h3>
              <div class="space-y-3">
                <div class="text-sm text-base-content/70">
                  No recent CRM activity
                </div>
                <RouterLink to="/crm/communications" class="btn btn-sm btn-ghost">
                  View All Activity
                </RouterLink>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Notifications Tab -->
      <div v-if="activeTab === 'notifications'" class="space-y-6">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-2xl font-bold">Push Notifications</h2>
            <p class="text-base-content/70 mt-1">Manage your notification preferences and settings</p>
          </div>
        </div>

        <NotificationManager />
      </div>

      <!-- Cache Tab -->
      <div v-if="activeTab === 'cache'">
        <CacheManagement />
      </div>
    </div>

    <!-- Floating Theme Toggle (Demo) -->
    <div class="fixed bottom-6 right-6 z-50">
      <button
        @click="toggleTheme"
        class="btn btn-circle btn-lg bg-gradient-to-br from-primary to-secondary text-primary-content shadow-2xl hover:shadow-glow transition-all duration-300 hover:scale-110"
        title="Toggle Theme"
      >
        <svg v-if="isDarkTheme" class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"/>
        </svg>
        <svg v-else class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"/>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, markRaw } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useAnalyticsStore } from '@/stores/analytics'
import { useCRMStore } from '@/stores/crm'
import { crmService } from '@/services/crm'

import AnalyticsDashboard from '@/components/analytics/AnalyticsDashboard.vue'
import CacheManagement from '@/components/CacheManagement.vue'
import NotificationManager from '@/components/notifications/NotificationManager.vue'
import HomeIcon from '@/components/icons/HomeIcon.vue'
import ChartBarIcon from '@/components/icons/ChartBarIcon.vue'
import UserGroupIcon from '@/components/icons/UserGroupIcon.vue'

const router = useRouter()
const authStore = useAuthStore()
const analyticsStore = useAnalyticsStore()
const crmStore = useCRMStore()

// Initialize Firebase Analytics (simple version for dashboard)
import { trackEvent, trackPageView } from '@/plugins/firebase'

const analytics = {
  trackClick: (element, category = 'dashboard') => {
    trackEvent('click', {
      element,
      category,
      page: 'dashboard'
    })
  },
  trackTabChange: (tabName) => {
    trackEvent('tab_change', {
      tab_name: tabName,
      page: 'dashboard'
    })
  },
  trackFeatureUsage: (feature) => {
    trackEvent('feature_usage', {
      feature_name: feature,
      page: 'dashboard'
    })
  }
}

const activeTab = ref('overview')
const isDarkTheme = ref(false)

// Navigation tabs with SVG icons (cleaner approach)
const navigationTabs = ref([
  {
    id: 'overview',
    label: 'Overview',
    iconPath: 'M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'
  },
  {
    id: 'analytics',
    label: 'Analytics',
    iconPath: 'M3 13h2v8H3v-8zm4-6h2v14H7V7zm4-4h2v18h-2V3zm4 9h2v9h-2v-9z'
  },
  {
    id: 'leads',
    label: 'Leads',
    iconPath: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z'
  },
  {
    id: 'reports',
    label: 'Reports',
    iconPath: 'M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z'
  },
  {
    id: 'crm',
    label: 'CRM',
    iconPath: 'M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2z'
  },
  {
    id: 'notifications',
    label: 'Notifications',
    iconPath: 'M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z'
  },
  {
    id: 'cache',
    label: 'Cache',
    iconPath: 'M4 7v10c0 2.21 1.79 4 4 4h8c2.21 0 4-1.79 4-4V7c0-2.21-1.79-4-4-4H8c-2.21 0-4 1.79-4 4z'
  }
])

const { totalUsers, conversionRate, totalLeads, qualifiedLeads } = analyticsStore
const { totalCustomers, activeProjects, unreadCommunications } = crmStore

// Enhanced sample data
const topServices = ref([
  { name: 'Energy Audit', count: 45, percentage: 90 },
  { name: 'Solar Installation', count: 32, percentage: 64 },
  { name: 'Energy Efficiency', count: 28, percentage: 56 },
  { name: 'Monitoring', count: 15, percentage: 30 },
  { name: 'Compliance', count: 12, percentage: 24 }
])

const recentLeads = ref([])

// Fetch recent leads from CRM
const fetchRecentLeads = async () => {
  try {
    const response = await crmService.getCustomers({
      status: 'lead',
      limit: 5,
      sortBy: 'created_at',
      sortOrder: 'desc'
    })

    // Handle different response structures
    const leads = response?.data || response || []

    if (Array.isArray(leads)) {
      recentLeads.value = leads.map(lead => ({
        name: `${lead.firstName || ''} ${lead.lastName || ''}`.trim() || 'Unknown',
        email: lead.email || 'No email',
        service: lead.notes ? lead.notes.substring(0, 50) + '...' : 'General Inquiry',
        timeAgo: formatTimeAgo(lead.firstContactDate || lead.created_at),
        status: getLeadStatus(lead.status),
        initials: getInitials(lead.firstName, lead.lastName),
        id: lead.id
      }))
    } else {
      console.warn('Expected array of leads, got:', typeof leads)
      recentLeads.value = []
    }
  } catch (error) {
    console.error('Failed to fetch recent leads:', error)
    // Use mock data as fallback for development
    recentLeads.value = [
      {
        name: 'Maria Silva',
        email: '<EMAIL>',
        service: 'Energy Audit Inquiry',
        timeAgo: '2h ago',
        status: 'New',
        initials: 'MS',
        id: 1
      },
      {
        name: 'João Santos',
        email: '<EMAIL>',
        service: 'Solar Installation',
        timeAgo: '4h ago',
        status: 'Contacted',
        initials: 'JS',
        id: 2
      }
    ]
  }
}

// Helper functions
const formatTimeAgo = (dateString) => {
  if (!dateString) return 'Unknown'
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))

  if (diffInHours < 1) return 'Just now'
  if (diffInHours < 24) return `${diffInHours}h ago`
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays < 7) return `${diffInDays}d ago`
  return date.toLocaleDateString()
}

const getLeadStatus = (status) => {
  const statusMap = {
    'lead': 'New',
    'prospect': 'Contacted',
    'customer': 'Qualified',
    'inactive': 'Follow-up'
  }
  return statusMap[status] || 'New'
}

const getInitials = (firstName, lastName) => {
  return `${firstName?.charAt(0) || ''}${lastName?.charAt(0) || ''}`.toUpperCase()
}

// Utility functions
const getServiceBadgeClass = (index: number) => {
  const classes = [
    'bg-primary/20 text-primary',
    'bg-secondary/20 text-secondary',
    'bg-accent/20 text-accent',
    'bg-success/20 text-success',
    'bg-info/20 text-info'
  ]
  return classes[index] || 'bg-base-300 text-base-content'
}

const getServiceProgressClass = (index: number) => {
  const classes = [
    'bg-gradient-to-r from-primary to-primary-focus',
    'bg-gradient-to-r from-secondary to-secondary-focus',
    'bg-gradient-to-r from-accent to-accent-focus',
    'bg-gradient-to-r from-success to-success-focus',
    'bg-gradient-to-r from-info to-info-focus'
  ]
  return classes[index] || 'bg-base-content'
}

const toggleTheme = () => {
  const html = document.documentElement
  const currentTheme = html.getAttribute('data-theme')
  const newTheme = currentTheme === 'hlenergy-dark' ? 'hlenergy-light' : 'hlenergy-dark'

  html.setAttribute('data-theme', newTheme)
  isDarkTheme.value = newTheme === 'hlenergy-dark'

  // Save theme preference
  localStorage.setItem('theme', newTheme)

  console.log(`🎨 Theme switched to: ${newTheme}`)
}

const logout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Logout failed:', error)
  }
}

const exportReport = async () => {
  try {
    const blob = await analyticsStore.exportAnalyticsData('json')
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `hlenergy-report-${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  } catch (error) {
    console.error('Failed to export report:', error)
  }
}

onMounted(async () => {
  // Track dashboard page view
  trackPageView('dashboard', 'Admin Dashboard')

  // Initialize analytics
  await analyticsStore.initializeAnalyticsStore()

  // Fetch recent leads
  await fetchRecentLeads()

  // Set initial theme state
  const currentTheme = document.documentElement.getAttribute('data-theme')
  isDarkTheme.value = currentTheme === 'hlenergy-dark'

  console.log('📊 Dashboard initialized with theme:', currentTheme)
})
</script>

<style scoped>
/* Enhanced Dashboard Styles */
.dashboard-view {
  min-height: 100vh;
  transition: all 0.3s ease-in-out;
}

/* Glass Effect Header */
.glass-header {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Glass Effect Cards */
.glass-effect {
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease-in-out;
}

/* Stats Cards Hover Effects */
.stats-card:hover .glass-effect {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Theme-specific backgrounds */
[data-theme="hlenergy-light"] .dashboard-view {
  background: linear-gradient(135deg,
    hsl(var(--b1)) 0%,
    hsl(var(--b2)) 50%,
    hsl(var(--b3)) 100%);
}

[data-theme="hlenergy-dark"] .dashboard-view {
  background: linear-gradient(135deg,
    hsl(var(--b1)) 0%,
    hsl(var(--b2)) 50%,
    hsl(var(--b3)) 100%);
}

/* Enhanced card styles for light theme */
[data-theme="hlenergy-light"] .glass-effect {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(18, 129, 108, 0.1);
  box-shadow: 0 8px 32px rgba(18, 129, 108, 0.1);
}

/* Enhanced card styles for dark theme */
[data-theme="hlenergy-dark"] .glass-effect {
  background: rgba(10, 31, 27, 0.8);
  border: 1px solid rgba(92, 173, 100, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Glow effects */
.hover\:shadow-glow:hover {
  box-shadow: 0 0 20px rgba(18, 129, 108, 0.3);
}

.hover\:shadow-glow-gold:hover {
  box-shadow: 0 0 20px rgba(234, 170, 52, 0.3);
}

.hover\:shadow-glow-sage:hover {
  box-shadow: 0 0 20px rgba(92, 173, 100, 0.3);
}

/* Smooth transitions for all interactive elements */
.transition-all {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced button hover effects */
.btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Navigation tab active indicator animation */
@keyframes slideIn {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 50%;
    opacity: 1;
  }
}

.nav-indicator {
  animation: slideIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .glass-header {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .stats-card .card-body {
    padding: 1rem;
  }
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--b2));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--p) / 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--p) / 0.5);
}

/* Floating theme toggle */
.btn-circle.btn-lg {
  width: 4rem;
  height: 4rem;
}

.btn-circle:hover {
  transform: scale(1.1);
}

/* Enhanced mobile responsiveness */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .stats-card .text-3xl {
    font-size: 1.875rem;
  }

  .card-body {
    padding: 1rem;
  }
}
</style>
